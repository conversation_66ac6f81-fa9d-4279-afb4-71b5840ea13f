import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/multimedia_widgets.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/ocr_text_side_panel.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/resizable_panel.dart';

class TempWebChat extends StatefulWidget {
  const TempWebChat({super.key});

  @override
  State<TempWebChat> createState() => _TempWebChatState();
}

class _TempWebChatState extends State<TempWebChat> {
  final TextEditingController chatController = TextEditingController();
  bool isLoading = false;
  bool isAudioLoading =
      false; // Loading state for audio recording/transcription
  bool hasTextInChatField = false; // Track if there's text in the chat field

  // Helper method to access the provider's messages list
  List<ChatMessage> get messages =>
      Provider.of<WebHomeProvider>(context, listen: false).messages;

  // OCR side panel state
  bool showOcrPanel = false;
  String ocrText = '';
  String ocrFileName = '';

  // File upload state for the new UI
  bool isFileUploaded = false;
  bool isFileProcessing = false;
  String uploadedFileName = '';
  String uploadedFileText = '';
  FileUploadOcrResponse? fileUploadOcrResponse;

  // Multimedia service
  final MultimediaService _multimediaService = MultimediaService();

  // ScrollController for chat messages
  final ScrollController _chatScrollController = ScrollController();

  // Audio player
  final AudioPlayer audioPlayer = AudioPlayer();

  // Audio playback state
  String? currentPlayingMessageId;
  bool isPlaying = false;
  bool isPaused = false;
  Duration currentPosition = Duration.zero;
  Duration totalDuration = Duration.zero;
  String? currentAudioFilePath;

  // Recording state
  bool isRecording = false;

  // Speech recognition state
  String _recognizedText = "";

  // Side panel resizing
  double sidePanelWidth = 480.0; // Default width
  double minSidePanelWidth = 250.0; // Minimum width
  double maxSidePanelWidth = 600.0; // Maximum width
  bool isResizing = false;

  @override
  void initState() {
    super.initState();

    // Add listener to chat controller to detect text changes
    chatController.addListener(() {
      final hasText = chatController.text.trim().isNotEmpty;
      if (hasText != hasTextInChatField) {
        setState(() {
          hasTextInChatField = hasText;
        });
      }
    });

    // Initialize multimedia service
    _multimediaService.initialize();

    // Set up multimedia service callbacks
    _multimediaService.onStateChanged = () {
      if (mounted) {
        setState(() {
          // Update UI state based on multimedia service state
          isPlaying = _multimediaService.isPlaying;
          isPaused = _multimediaService.isPaused;
          isRecording = _multimediaService.isRecording;
          currentPlayingMessageId = _multimediaService.currentPlayingMessageId;
          currentPosition = _multimediaService.currentPosition;
          totalDuration = _multimediaService.totalDuration;
        });
      }
    };

    // Set up additional multimedia service listeners for text recognition
    _setupMultimediaServiceListeners();

    _multimediaService.onFileProcessed =
        (fileName, extractedText, ocrResponseModel) {
      if (mounted) {
        // Add file upload and OCR messages to chat
        setState(() {
          ocrFileName = fileName;
          ocrText = extractedText;
          showOcrPanel = true;
          chatController.text = extractedText;
          fileUploadOcrResponse = ocrResponseModel;
        });

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    };
  }

  @override
  void dispose() {
    // Clean up the controllers when the widget is disposed
    chatController.dispose();
    _chatScrollController.dispose();

    // Dispose audio player
    audioPlayer.dispose();

    // Dispose recording service
    _multimediaService.dispose();

    super.dispose();
  }

  // Set up multimedia service listeners for text recognition
  void _setupMultimediaServiceListeners() {
    if (!mounted) return;

    try {
      // Set up a listener for recognized text
      _multimediaService.onTextRecognized = (text) {
        if (mounted) {
          setState(() {
            // Update recognized text in the chat field
            _recognizedText = text;
            chatController.text = text;

            // Log the recognized text
            Logger.info('Text recognized from audio: $text');
          });
        }
      };
    } catch (e) {
      Logger.error('Error setting up multimedia service listeners: $e');
    }
  }

  // Scroll to the bottom of the chat
  void _scrollToBottom() {
    if (_chatScrollController.hasClients) {
      _chatScrollController.animateTo(
        _chatScrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  // Add file upload and OCR messages to chat
  void addFileUploadAndOcrMessages(String fileName, String extractedText,
      FileUploadOcrResponse ocrResponseModel) {
    setState(() {
      ocrFileName = fileName;
      ocrText = extractedText;
      showOcrPanel = true;
      chatController.text = extractedText;

      // Also update these for the ChatField component
      isFileUploaded = true;
      isFileProcessing = false; // OCR is complete, stop loading
      uploadedFileName = fileName;
      uploadedFileText = extractedText;
      fileUploadOcrResponse = ocrResponseModel;
    });

    // Store the extracted text in the provider for the next API call
    final provider = Provider.of<WebHomeProvider>(context, listen: false);
    provider.lastUserMessageForApi = extractedText;

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  // Handle file selection from the ChatField component
  void _handleFileSelection(String fileName, String filePath) {
    // Show loading indicator
    setState(() {
      isLoading = true;
    });

    // Use the multimedia service to process the file
    _multimediaService.onFileProcessed =
        (fileName, extractedText, ocrResponseModel) {
      // Add the file upload and OCR messages to chat
      addFileUploadAndOcrMessages(fileName, extractedText, ocrResponseModel);

      setState(() {
        isLoading = false;
      });
    };

    // Process the file
    _multimediaService.processFile(context);
  }

  // Toggle OCR panel
  void toggleOcrPanel() {
    setState(() {
      showOcrPanel = !showOcrPanel;
    });
  }

  // Close OCR panel
  void closeOcrPanel() {
    setState(() {
      showOcrPanel = false;
    });
  }

  // Convert text to speech and play audio
  Future<void> _convertTextToSpeech(String text, {String? messageId}) async {
    if (text.isEmpty) return;

    // Show loading indicator
    setState(() {
      isLoading = true;
    });

    try {
      // Call the multimedia service to convert text to speech
      await _multimediaService.convertTextToSpeech(
        text,
        messageId: messageId,
        context: context,
      );
    } finally {
      // Hide loading indicator
      setState(() {
        isLoading = false;
      });
    }
  }

  // Toggle recording (start/stop)
  Future<void> _toggleRecording() async {
    await _multimediaService.toggleRecording(context);
  }

  // Handle sending a message
  void _sendMessage() async {
    String text = chatController.text.trim();
    if (text.isEmpty) {
      if (fileUploadOcrResponse != null &&
          fileUploadOcrResponse?.data?.originalText != null) {
        text = fileUploadOcrResponse!.data!.originalText!;
      } else {
        return;
      }
    }

    // Clear the chat input field
    chatController.clear();

    // Add user message to chat
    final provider = Provider.of<WebHomeProvider>(context, listen: false);
    provider.addMessage(ChatMessage(
      content: text,
      isUser: true,
    ));

    // Reset file upload state
    setState(() {
      isFileUploaded = false;
      isFileProcessing = false;
      uploadedFileName = '';
      uploadedFileText = '';
      fileUploadOcrResponse = null;
      showOcrPanel = false;
    });

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  // Cancel the current request
  void _cancelRequest() async {
    // Get the provider before any async operations
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // If recording, stop it
    if (isRecording) {
      await _multimediaService.stopSpeechRecognition();
      Logger.info('Speech recognition stopped by cancel request');
    }

    // Check if widget is still mounted after async operations
    if (!mounted) return;

    // Update state using the provider and local state
    provider.isLoading = false;

    // Add a message to indicate the request was cancelled
    provider.addMessage(ChatMessage(
      content: 'Request cancelled by user.',
      isUser: false,
    ));

    // Update local recording state
    setState(() {
      isLoading = false;
      isRecording = false;
    });

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _scrollToBottom();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Row(
          children: [
            // Main chat area
            Expanded(
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: EdgeInsets.all(AppSpacing.md),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'Multimedia Chat Demo',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                        Spacer(),
                        if (showOcrPanel)
                          OcrPanelToggleButton(
                            isShown: showOcrPanel,
                            onToggle: toggleOcrPanel,
                          ),
                      ],
                    ),
                  ),

                  // Chat messages area
                  Expanded(
                    child: Container(
                      color: Colors.white,
                      child: Consumer<WebHomeProvider>(
                        builder: (context, provider, child) {
                          final messages = provider.messages;

                          if (messages.isEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.chat_bubble_outline,
                                    size: 64,
                                    color: Colors.grey.shade400,
                                  ),
                                  SizedBox(height: AppSpacing.md),
                                  Text(
                                    'Start a conversation with multimedia features',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey.shade600,
                                      fontFamily: 'TiemposText',
                                    ),
                                  ),
                                  SizedBox(height: AppSpacing.sm),
                                  Text(
                                    'Upload files, record audio, or type a message',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey.shade500,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          return ListView.builder(
                            controller: _chatScrollController,
                            padding: EdgeInsets.all(AppSpacing.md),
                            itemCount: messages.length,
                            itemBuilder: (context, index) {
                              final message = messages[index];
                              return _buildMessageBubble(message, index);
                            },
                          );
                        },
                      ),
                    ),
                  ),

                  // Chat input field
                  Container(
                    padding: EdgeInsets.all(AppSpacing.md),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        top: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),
                    child: ChatField(
                      isLoading: isLoading,
                      parentState: this,
                      onSendMessage: _sendMessage,
                      onCancelRequest: _cancelRequest,
                      onFileSelected: _handleFileSelection,
                      onToggleRecording: _toggleRecording,
                      controller: chatController,
                      multimediaService: _multimediaService,
                      initialUploadedFileName: uploadedFileName,
                      initialUploadedFileText: uploadedFileText,
                      initialIsFileUploaded: isFileUploaded,
                      initialIsFileProcessing: isFileProcessing,
                      onFileUploadTap: toggleOcrPanel,
                      onFileCloseTap: closeOcrPanel,
                    ),
                  ),
                ],
              ),
            ),

            // OCR side panel
            if (showOcrPanel)
              ResizablePanel(
                width: sidePanelWidth,
                minWidth: minSidePanelWidth,
                maxWidth: maxSidePanelWidth,
                onResize: (newWidth) {
                  setState(() {
                    sidePanelWidth = newWidth;
                  });
                },
                child: OcrTextSidePanel(
                  fileName: ocrFileName,
                  ocrText: ocrText,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Build message bubble widget
  Widget _buildMessageBubble(ChatMessage message, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            // AI avatar
            Container(
              width: 32,
              height: 32,
              margin: EdgeInsets.only(right: AppSpacing.xs),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.smart_toy,
                size: 20,
                color: Colors.blue.shade700,
              ),
            ),
          ],

          // Message content
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.7,
              ),
              padding: EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: message.isUser
                    ? Colors.blue.shade500
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(AppSpacing.sm),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Message text
                  SelectableText(
                    message.content,
                    style: TextStyle(
                      color: message.isUser ? Colors.white : Colors.black87,
                      fontSize: 14,
                    ),
                  ),

                  // Message actions for AI messages
                  if (!message.isUser) ...[
                    SizedBox(height: AppSpacing.xs),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Text-to-speech button
                        InkWell(
                          onTap: () => _convertTextToSpeech(
                            message.content,
                            messageId: index.toString(),
                          ),
                          child: Container(
                            padding: EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Icon(
                              currentPlayingMessageId == index.toString() &&
                                      isPlaying
                                  ? Icons.pause
                                  : Icons.volume_up,
                              size: 16,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                        SizedBox(width: AppSpacing.xs),

                        // Copy button
                        InkWell(
                          onTap: () {
                            Clipboard.setData(
                                ClipboardData(text: message.content));
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Message copied to clipboard'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          },
                          child: Container(
                            padding: EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Icon(
                              Icons.copy,
                              size: 16,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),

          if (message.isUser) ...[
            // User avatar
            Container(
              width: 32,
              height: 32,
              margin: EdgeInsets.only(left: AppSpacing.xs),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.person,
                size: 20,
                color: Colors.green.shade700,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
