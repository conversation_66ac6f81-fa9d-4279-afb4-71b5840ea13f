# Multimedia Chat Demo - NEW TEMP MODE BEHAVIOR

This directory contains the implementation of multimedia functionality with a new file upload workflow specifically designed for the temp chat interface.

## NEW File Upload Behavior (Temp Mode)

### **Current Implementation:**
When `isTemp: true` is passed to <PERSON><PERSON><PERSON><PERSON> (as in temp_web_chat.dart):

1. **File Selection Only**: User uploads a file → Only file selection occurs, NO immediate OCR processing
2. **Pending State**: File is stored in `pendingFile` variable and shown in FileUploadPreview without processing
3. **Wait for User Input**: User can type a message/question about the file
4. **Combined API Call**: When user hits send → Single API call processes:
   - The selected file for OCR processing
   - The user's typed message/prompt
   - Both are processed together in one request

### **Message Flow:**
1. User selects file → FileUploadPreview appears (no processing yet)
2. User types message → "What does this document say?"
3. User hits send → Combined processing begins
4. User message appears: "Uploaded file: document.pdf\n\nWhat does this document say?"
5. System processes file and responds with FileUploadResponsePreview + OCR text sections

### **Benefits:**
- More intentional workflow where users provide context about files
- Single API call instead of separate file processing + message sending
- Better user experience for document analysis with specific questions

## Files Modified

### **temp_web_chat.dart**
- Added `pendingFile` property for storing unprocessed files
- Modified `_sendMessage()` to check for pending files
- Added `_processPendingFileWithMessage()` method for combined processing
- Passes `isTemp: true` to ChatField component

### **chat_field.dart**
- Added `isTemp` parameter to distinguish temp vs normal behavior
- Modified `_sendMessage()` to allow sending with file in temp mode
- Updated file close handler to clear pending files in temp mode

### **add_button_with_menu.dart**
- Added conditional logic in `_uploadFile()` method
- For temp mode: Only file selection, no immediate OCR processing
- For normal mode: Continues with existing immediate processing behavior

## Technical Implementation

### **State Management:**
```dart
// Pending file for temp mode (file selected but not processed yet)
dynamic pendingFile;

// Check if we have a pending file to process (temp mode)
if (pendingFile != null) {
  await _processPendingFileWithMessage(text);
  return;
}
```

### **Combined Processing:**
```dart
Future<void> _processPendingFileWithMessage(String userMessage) async {
  // 1. Add user message (file + text)
  // 2. Process file for OCR
  // 3. Create system response with file data
  // 4. Update OCR panel state
  // 5. Clear pending file
}
```

### **Conditional File Upload:**
```dart
// Check if this is temp mode
final isTemp = widget.parentState.widget?.isTemp ?? false;

if (isTemp) {
  // Only select file, don't process OCR immediately
  widget.parentState.pendingFile = file;
  return;
}
// Continue with normal immediate processing
```

## Usage

```dart
// For temp chat interface
ChatField(
  isTemp: true,  // Enables new file upload behavior
  // ... other parameters
)

// For normal chat interface  
ChatField(
  isTemp: false, // or omit (defaults to false)
  // ... other parameters
)
```

## Backward Compatibility

- Normal chat interfaces (isTemp: false) continue with existing immediate OCR processing
- Only temp chat interfaces (isTemp: true) use the new deferred processing workflow
- All existing functionality preserved for non-temp usage

## Error Handling

- File selection cancellation properly handled
- OCR processing errors display appropriate error messages
- Pending file state properly cleared on errors
- Loading states managed throughout the process
