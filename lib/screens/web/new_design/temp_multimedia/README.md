# Multimedia Chat Demo

This directory contains the implementation of multimedia functionality replicated from `web_home_screen_chat.dart` into `temp_web_chat.dart`.

## Features Implemented

### ✅ File Upload Functionality
- **User Message Display**: When a user uploads a file, it shows as a user message bubble with "Uploaded file: [filename]"
- **System Response Structure**: AI response contains:
  - `FileUploadResponsePreview` component showing the uploaded file details
  - "Original Text" section with raw OCR-extracted text
  - "Corrected Text" section with AI-processed/corrected text (if different from original)
- **Styling**: Matches exact message bubble theme, colors, and layout from `web_home_screen_chat.dart`
- **API Integration**: Uses adapter-based endpoint (http://**********:8001/adapters with OCR service)

### ✅ Speech-to-Text Functionality
- Audio recording UI and controls
- Transcription API integration using adapter-based endpoint with multipart/form-data
- Audio file handling and processing logic
- Web audio recorder support

### ✅ Text-to-Speech Functionality
- TTS controls and UI elements
- TTS API integration using adapter-based endpoint
- Audio playback functionality with play/pause controls

### ✅ Translation Functionality
- Translation UI controls and language selection
- Translation API integration using adapter-based endpoint
- Language switching and text translation logic

### ✅ OCR Side Panel
- Displays extracted text with copy functionality
- Resizable panel with proper width management
- Toggle functionality integrated with file upload

## Message Flow

1. **User uploads file** → User message bubble appears with "Uploaded file: [filename]"
2. **System processes file** → System message bubble appears with:
   - FileUploadResponsePreview component
   - Original Text section
   - Corrected Text section (if available and different)

## Styling Details

- **User Messages**: Light gray background (#F5F5F5) with border (#D0D0D0)
- **System Messages**: White background with proper spacing and typography
- **Font**: TiemposText family, responsive sizing (15px/17px based on screen width)
- **Layout**: Matches original implementation with proper constraints and alignment

## Authentication & Error Handling

- Uses getValidToken from AuthService for all API calls
- Implements 401 retry logic through MultimediaService
- Shows alerts for non-200 status codes
- Consistent NSL-branded loading experience with NSLKnowledgeLoaderWrapper

## Usage

```dart
import 'temp_multimedia_demo.dart';

// Use TempMultimediaDemo widget which provides necessary Provider context
TempMultimediaDemo()
```

## Files

- `temp_web_chat.dart` - Main chat interface with multimedia functionality
- `temp_multimedia_demo.dart` - Demo wrapper with Provider context
- `README.md` - This documentation file

## Dependencies

All required imports and dependencies have been added:
- MultimediaService for multimedia operations
- ChatField for input handling
- FileUploadResponsePreview for file display
- OcrTextSidePanel for OCR text display
- ResizablePanel for side panel functionality
- NSLKnowledgeLoaderWrapper for loading states
