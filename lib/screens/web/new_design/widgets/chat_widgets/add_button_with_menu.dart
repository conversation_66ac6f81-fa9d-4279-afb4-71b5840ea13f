import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/multimedia_widgets.dart';
import 'package:nsl/services/file_upload_ocr_service.dart';
import 'package:nsl/services/file_upload_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';

class AddButtonWithMenu extends StatefulWidget {
  final dynamic parentState;

  const AddButtonWithMenu(this.parentState, {super.key});

  @override
  State<AddButtonWithMenu> createState() => AddButtonWithMenuState();
}

class AddButtonWithMenuState extends State<AddButtonWithMenu> {
  bool isHovered = false;
  bool isMenuOpen = false;
  bool isLoading = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  // File upload state
  bool isFileUploaded = false;
  String uploadedFileName = '';
  String extractedText = '';

  @override
  void dispose() {
    _removeMenu();
    super.dispose();
  }

  void _toggleMenu() {
    if (isMenuOpen) {
      _removeMenu();
    } else {
      _showMenu();
    }
  }

  void _removeMenu() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      isMenuOpen = false;
    });
  }

  void _showMenu() {
    _removeMenu();

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // Set a fixed menu height
    final menuHeight = 100; // Approximate height of the menu

    // Get screen height to determine if menu should appear above or below
    final screenHeight = MediaQuery.of(context).size.height;

    // Check if there's enough space below the button
    final spaceBelow = screenHeight - (offset.dy + size.height);
    final showAbove = spaceBelow < menuHeight + 20; // Add some buffer space

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        // Position above or below based on available space, with minimal gap
        top: showAbove
            ? offset.dy - menuHeight + 15 // Position above with small gap
            : offset.dy + size.height - 5, // Position below with small gap
        child: Material(
          // elevation: 4.0,
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
          child: Container(
            width: 190,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
              border: Border.all(color: Color(0xffC1C1C1), width: 1),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AddButtonMenuItem(
                  imagePath: 'assets/images/upload_file.svg',
                  text: 'Upload A File',
                  onTap: () async {
                    _removeMenu();
                    await _uploadFile();
                  },
                ),
                AddButtonMenuItem(
                  imagePath: 'assets/images/screenshot.svg',
                  text: 'Take A Screenshot',
                  onTap: () {
                    _removeMenu();
                    // Handle screenshot action
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      isMenuOpen = true;
    });
  }

  // Upload file dispatcher - determines which upload method to use
  Future<void> _uploadFile() async {
    // Check if this is temp mode (isTemp parameter from ChatField)
    final isTemp = widget.parentState.isTemp ?? false;

    if (isTemp) {
      await _tempFileUpload();
    } else {
      await _normalFileUpload();
    }
  }

  // Handle file upload for temp mode (file selection only, no processing)
  Future<void> _tempFileUpload() async {
    final fileUploadService = FileUploadService();

    // Show loading state
    isLoading = true;

    try {
      // Only pick the file, no processing
      final file = await fileUploadService.pickFile();

      if (file == null) {
        // User canceled the picker
        isLoading = false;
        return;
      }

      // Update local state to show file upload preview
      setState(() {
        isFileUploaded = true;
        uploadedFileName = file.name;
        extractedText = '';
      });

      // Update parent state to show file upload preview without processing
      widget.parentState.setState(() {
        widget.parentState.uploadedFileName = file.name;
        widget.parentState.uploadedFileText = '';
        widget.parentState.isFileUploaded = true;
        widget.parentState.isFileProcessing = false; // Not processing yet
        widget.parentState.pendingFile =
            file; // Store the file for later processing
      });

      // Hide loading state
      isLoading = false;
    } catch (e) {
      // Handle error
      isLoading = false;

      setState(() {
        isFileUploaded = false;
        uploadedFileName = '';
        extractedText = '';
      });

      widget.parentState.setState(() {
        widget.parentState.uploadedFileName = '';
        widget.parentState.uploadedFileText = '';
        widget.parentState.isFileUploaded = false;
        widget.parentState.isFileProcessing = false;
        widget.parentState.pendingFile = null;
      });

      final fileUploadOcrService = FileUploadOcrService();
      if (mounted) {
        fileUploadOcrService.showOverlay(
          context,
          'Error selecting file: $e',
          isError: true,
        );
      }
    }
  }

  // Handle file upload for normal mode (immediate processing)
  Future<void> _normalFileUpload() async {
    final fileUploadOcrService = FileUploadOcrService();
    final fileUploadService = FileUploadService();

    // Show loading state
    isLoading = true;

    try {
      // First, pick the file to get the file name immediately
      final file = await fileUploadService.pickFile();

      if (file == null) {
        // User canceled the picker
        isLoading = false;
        return;
      }

      // Show FileUploadPreview immediately with loading state
      setState(() {
        isFileUploaded = true;
        uploadedFileName = file.name;
        extractedText = '';
      });

      // Update parent state to show preview immediately
      widget.parentState.setState(() {
        widget.parentState.uploadedFileName = file.name;
        widget.parentState.uploadedFileText = '';
        widget.parentState.isFileUploaded = true;
        widget.parentState.isFileProcessing = true;
      });

      // Now process the file for OCR
      final uploadResult = await fileUploadService.uploadFile(file);

      // Hide loading state
      isLoading = false;

      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      if (!uploadResult['success']) {
        // Handle upload error - remove the preview
        setState(() {
          isFileUploaded = false;
          uploadedFileName = '';
          extractedText = '';
        });

        widget.parentState.setState(() {
          widget.parentState.uploadedFileName = '';
          widget.parentState.uploadedFileText = '';
          widget.parentState.isFileUploaded = false;
          widget.parentState.isFileProcessing = false;
        });

        fileUploadOcrService.showOverlay(
          context,
          'Failed to upload file: ${uploadResult['message']}',
          isError: true,
        );
        return;
      }

      // Success - update with extracted text and remove loading state
      final extractedTextResult = uploadResult['extracted_text'];

      setState(() {
        extractedText = extractedTextResult;
      });

      // Update parent state with final results
      widget.parentState.setState(() {
        widget.parentState.uploadedFileText = extractedTextResult;
        widget.parentState.isFileProcessing = false;
        widget.parentState.fileUploadOcrResponse =
            uploadResult['file_upload_ocr_response'];

        // Also update OCR panel if available
        if (widget.parentState.ocrFileName != null) {
          widget.parentState.ocrFileName = file.name;
          widget.parentState.ocrText = extractedTextResult;
        }
      });

      // Show success message
      fileUploadOcrService.showOverlay(
        context,
        'File uploaded: ${file.name}',
      );

      // Log the extracted text
      Logger.info(
          'Extracted text to be sent with next message: $extractedTextResult');
    } catch (e) {
      // Hide loading state and remove preview on error
      isLoading = false;

      setState(() {
        isFileUploaded = false;
        uploadedFileName = '';
        extractedText = '';
      });

      widget.parentState.setState(() {
        widget.parentState.uploadedFileName = '';
        widget.parentState.uploadedFileText = '';
        widget.parentState.isFileUploaded = false;
        widget.parentState.isFileProcessing = false;
      });

      fileUploadOcrService.showOverlay(
        context,
        'Error: $e',
        isError: true,
      );
    }
  }

  // Show loading overlay

  // Hide loading overlay

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        cursor: SystemMouseCursors.click,
        child: Container(
          height: isLoading ? 16 : 28,
          width: isLoading ? 16 : 28,
          margin: EdgeInsets.symmetric(
              horizontal: AppSpacing.xs, vertical: AppSpacing.sm),
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
              color: isLoading
                  ? Colors.transparent
                  : isMenuOpen
                      ? Theme.of(context).colorScheme.primary
                      : isHovered
                          ? Theme.of(context).colorScheme.primary
                          : Color(0xffE4EDFF),
              borderRadius: BorderRadius.circular(AppSpacing.lg)),
          child:
              // isLoading
              // ? CircularProgressIndicator(
              //     strokeWidth: 2,
              //     valueColor: AlwaysStoppedAnimation<Color>(
              //       Color(0xff0058FF),
              //     ),
              //   )
              // :
              IconButton(
            padding: EdgeInsets.zero,
            onPressed: _toggleMenu,
            icon: Icon(isMenuOpen ? Icons.close : Icons.add),
            iconSize: 18,
            color: isMenuOpen || isHovered ? Colors.white : null,
          ),
        ),
      ),
    );
  }
}
